{"expo": {"name": "GuzoSyncApp", "slug": "GuzoSyncApp", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "guzosyncapp", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "package": "com.anonymous.GuzoSyncApp"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}], ["expo-location", {"locationAlwaysAndWhenInUsePermission": "Allow GuzoSyncApp to use your location to show your position on the map and provide route guidance."}], ["expo-maps", {"requestLocationPermission": true, "locationPermission": "Allow GuzoSyncApp to use your location to show your position on the map and provide route guidance."}]], "experiments": {"typedRoutes": true}}}