# Guzo Sync Backend API Configuration
# Copy this file to .env and update the values for your environment

# Backend API Base URL
# Development
EXPO_PUBLIC_API_BASE_URL=http://localhost:3000

# Production (uncomment and update when deploying)
# EXPO_PUBLIC_API_BASE_URL=https://api.guzosync.com

# API Configuration
EXPO_PUBLIC_API_TIMEOUT=30000
EXPO_PUBLIC_API_VERSION=v1

# Environment
EXPO_PUBLIC_ENVIRONMENT=development

# Debug settings
EXPO_PUBLIC_DEBUG_API=true
EXPO_PUBLIC_DEBUG_AUTH=true
