import { AUTH_CONSTANTS } from '@/constants/Auth';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { API_CONFIG, API_ERROR_TYPES, HTTP_STATUS, TIMEOUT_CONFIG } from './config';

/**
 * Custom API Error class
 */
export class ApiError extends Error {
  constructor(
    message: string,
    public status?: number,
    public type?: string,
    public data?: any
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

/**
 * HTTP Client for API requests
 */
class HttpClient {
  private baseURL: string;
  private defaultHeaders: Record<string, string>;

  constructor() {
    this.baseURL = API_CONFIG.BASE_URL;
    this.defaultHeaders = { ...API_CONFIG.DEFAULT_HEADERS };
  }

  /**
   * Get authentication token from storage
   */
  private async getAuthToken(): Promise<string | null> {
    try {
      return await AsyncStorage.getItem(AUTH_CONSTANTS.STORAGE_KEYS.TOKEN);
    } catch (error) {
      console.error('Error getting auth token:', error);
      return null;
    }
  }

  /**
   * Build request headers
   */
  private async buildHeaders(customHeaders?: Record<string, string>): Promise<Record<string, string>> {
    const headers = { ...this.defaultHeaders };
    
    // Add authentication token if available
    const token = await this.getAuthToken();
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }

    // Add custom headers
    if (customHeaders) {
      Object.assign(headers, customHeaders);
    }

    return headers;
  }

  /**
   * Handle API response
   */
  private async handleResponse<T>(response: Response): Promise<T> {
    const contentType = response.headers.get('content-type');
    const isJson = contentType?.includes('application/json');

    let data: any;
    try {
      data = isJson ? await response.json() : await response.text();
    } catch (error) {
      throw new ApiError(
        'Failed to parse response',
        response.status,
        API_ERROR_TYPES.UNKNOWN_ERROR
      );
    }

    if (!response.ok) {
      const errorMessage = data?.message || data?.error || `HTTP ${response.status}`;
      const errorType = this.getErrorType(response.status);
      
      throw new ApiError(
        errorMessage,
        response.status,
        errorType,
        data
      );
    }

    return data;
  }

  /**
   * Get error type based on status code
   */
  private getErrorType(status: number): string {
    switch (status) {
      case HTTP_STATUS.BAD_REQUEST:
      case HTTP_STATUS.UNPROCESSABLE_ENTITY:
        return API_ERROR_TYPES.VALIDATION_ERROR;
      case HTTP_STATUS.UNAUTHORIZED:
        return API_ERROR_TYPES.AUTHENTICATION_ERROR;
      case HTTP_STATUS.FORBIDDEN:
        return API_ERROR_TYPES.AUTHORIZATION_ERROR;
      case HTTP_STATUS.INTERNAL_SERVER_ERROR:
      case HTTP_STATUS.SERVICE_UNAVAILABLE:
        return API_ERROR_TYPES.SERVER_ERROR;
      default:
        return API_ERROR_TYPES.UNKNOWN_ERROR;
    }
  }

  /**
   * Debug log for API requests
   */
  private debugLog(message: string, data?: any): void {
    if (API_CONFIG.DEBUG_API) {
      console.log(`[API] ${message}`, data || '');
    }
  }

  /**
   * Make HTTP request
   */
  private async request<T>(
    endpoint: string,
    options: RequestInit = {},
    timeout: number = TIMEOUT_CONFIG.DEFAULT
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;
    const headers = await this.buildHeaders(options.headers as Record<string, string>);

    this.debugLog(`${options.method || 'GET'} ${url}`, {
      headers: { ...headers, Authorization: headers.Authorization ? '[REDACTED]' : undefined },
      body: options.body ? JSON.parse(options.body as string) : undefined,
    });

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    try {
      const response = await fetch(url, {
        ...options,
        headers,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      this.debugLog(`Response ${response.status} ${response.statusText}`, {
        status: response.status,
        headers: Object.fromEntries(response.headers.entries()),
      });

      return await this.handleResponse<T>(response);
    } catch (error) {
      clearTimeout(timeoutId);

      this.debugLog('Request failed', error);

      if (error instanceof ApiError) {
        throw error;
      }

      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw new ApiError(
            'Request timeout',
            0,
            API_ERROR_TYPES.TIMEOUT_ERROR
          );
        }

        throw new ApiError(
          'Network error',
          0,
          API_ERROR_TYPES.NETWORK_ERROR,
          error.message
        );
      }

      throw new ApiError(
        'Unknown error occurred',
        0,
        API_ERROR_TYPES.UNKNOWN_ERROR
      );
    }
  }

  /**
   * GET request
   */
  async get<T>(endpoint: string, timeout?: number): Promise<T> {
    return this.request<T>(endpoint, { method: 'GET' }, timeout);
  }

  /**
   * POST request
   */
  async post<T>(endpoint: string, data?: any, timeout?: number): Promise<T> {
    return this.request<T>(
      endpoint,
      {
        method: 'POST',
        body: data ? JSON.stringify(data) : undefined,
      },
      timeout
    );
  }

  /**
   * PUT request
   */
  async put<T>(endpoint: string, data?: any, timeout?: number): Promise<T> {
    return this.request<T>(
      endpoint,
      {
        method: 'PUT',
        body: data ? JSON.stringify(data) : undefined,
      },
      timeout
    );
  }

  /**
   * DELETE request
   */
  async delete<T>(endpoint: string, timeout?: number): Promise<T> {
    return this.request<T>(endpoint, { method: 'DELETE' }, timeout);
  }

  /**
   * PATCH request
   */
  async patch<T>(endpoint: string, data?: any, timeout?: number): Promise<T> {
    return this.request<T>(
      endpoint,
      {
        method: 'PATCH',
        body: data ? JSON.stringify(data) : undefined,
      },
      timeout
    );
  }
}

// Export singleton instance
export const httpClient = new HttpClient();
