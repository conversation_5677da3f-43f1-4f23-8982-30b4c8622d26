import { AUTH_CONSTANTS } from '@/constants/Auth';
import type { AuthResponse, Driver, LoginCredentials } from '@/types/auth';
import { API_ENDPOINTS, API_ERROR_TYPES, TIMEOUT_CONFIG } from './config';
import { ApiError, httpClient } from './httpClient';

/**
 * Login request payload based on backend API
 */
interface LoginRequest {
  email: string;
  password: string;
}

/**
 * Login response from backend API
 */
interface LoginApiResponse {
  success: boolean;
  message: string;
  data?: {
    user: {
      id: string;
      email: string;
      firstName: string;
      lastName: string;
      phoneNumber?: string;
      role: string;
      isActive: boolean;
      createdAt: string;
      updatedAt: string;
      // Driver-specific fields
      licenseNumber?: string;
      employeeId?: string;
      profileImage?: string;
    };
    token: string;
    refreshToken?: string;
    expiresIn: number;
  };
  error?: string;
}

/**
 * Profile response from backend API
 */
interface ProfileApiResponse {
  success: boolean;
  data?: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    phoneNumber?: string;
    role: string;
    isActive: boolean;
    createdAt: string;
    updatedAt: string;
    // Driver-specific fields
    licenseNumber?: string;
    employeeId?: string;
    profileImage?: string;
  };
  message: string;
  error?: string;
}

/**
 * Authentication Service
 * Handles all authentication-related API calls
 */
export class AuthService {
  /**
   * Mock login for development when backend is not available
   */
  private async mockLogin(credentials: LoginCredentials): Promise<AuthResponse> {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 1500));

    const mockDriver: Driver = {
      id: 'mock_driver_1',
      email: credentials.email,
      firstName: 'John',
      lastName: 'Doe',
      phoneNumber: '+**********',
      licenseNumber: 'DL123456',
      employeeId: 'EMP001',
      isActive: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    return {
      success: true,
      message: 'Login successful (Mock)',
      data: {
        driver: mockDriver,
        token: 'mock_jwt_token_' + Date.now(),
        refreshToken: 'mock_refresh_token_' + Date.now(),
        expiresIn: 3600,
      },
    };
  }

  /**
   * Login user with email and password
   */
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    try {
      const loginRequest: LoginRequest = {
        email: credentials.email.toLowerCase().trim(),
        password: credentials.password,
      };

      const response = await httpClient.post<LoginApiResponse>(
        API_ENDPOINTS.AUTH.LOGIN,
        loginRequest,
        TIMEOUT_CONFIG.AUTHENTICATION
      );

      // Transform backend response to our AuthResponse format
      if (response.success && response.data) {
        const driver: Driver = {
          id: response.data.user.id,
          email: response.data.user.email,
          firstName: response.data.user.firstName,
          lastName: response.data.user.lastName,
          phoneNumber: response.data.user.phoneNumber || '',
          licenseNumber: response.data.user.licenseNumber || '',
          employeeId: response.data.user.employeeId || '',
          profileImage: response.data.user.profileImage,
          isActive: response.data.user.isActive,
          createdAt: response.data.user.createdAt,
          updatedAt: response.data.user.updatedAt,
        };

        return {
          success: true,
          message: response.message,
          data: {
            driver,
            token: response.data.token,
            refreshToken: response.data.refreshToken || '',
            expiresIn: response.data.expiresIn,
          },
        };
      }

      return {
        success: false,
        message: response.message || 'Login failed',
        error: response.error || 'Invalid response from server',
      };
    } catch (error) {
      console.error('Login error:', error);

      if (error instanceof ApiError) {
        // Handle specific API errors
        switch (error.type) {
          case API_ERROR_TYPES.AUTHENTICATION_ERROR:
            return {
              success: false,
              message: 'Invalid email or password',
              error: AUTH_CONSTANTS.ERRORS.INVALID_CREDENTIALS,
            };
          case API_ERROR_TYPES.VALIDATION_ERROR:
            return {
              success: false,
              message: 'Invalid input data',
              error: error.message,
            };
          case API_ERROR_TYPES.NETWORK_ERROR:
            // Fallback to mock login in development when backend is not available
            if (__DEV__) {
              console.warn('Backend not available, using mock login');
              return await this.mockLogin(credentials);
            }
            return {
              success: false,
              message: 'Network error',
              error: AUTH_CONSTANTS.ERRORS.NETWORK_ERROR,
            };
          case API_ERROR_TYPES.TIMEOUT_ERROR:
            return {
              success: false,
              message: 'Request timeout',
              error: 'Login request timed out. Please try again.',
            };
          case API_ERROR_TYPES.SERVER_ERROR:
            return {
              success: false,
              message: 'Server error',
              error: AUTH_CONSTANTS.ERRORS.SERVER_ERROR,
            };
          default:
            return {
              success: false,
              message: 'Login failed',
              error: error.message || AUTH_CONSTANTS.ERRORS.UNKNOWN_ERROR,
            };
        }
      }

      // Fallback to mock in development for any other errors
      if (__DEV__) {
        console.warn('Login failed, using mock login in development');
        return await this.mockLogin(credentials);
      }

      return {
        success: false,
        message: 'Login failed',
        error: AUTH_CONSTANTS.ERRORS.UNKNOWN_ERROR,
      };
    }
  }

  /**
   * Logout user
   */
  async logout(): Promise<{ success: boolean; message: string }> {
    try {
      await httpClient.post(API_ENDPOINTS.AUTH.LOGOUT);
      return {
        success: true,
        message: 'Logged out successfully',
      };
    } catch (error) {
      console.error('Logout error:', error);
      // Even if logout fails on server, we should still clear local data
      return {
        success: true,
        message: 'Logged out locally',
      };
    }
  }

  /**
   * Get current user profile
   */
  async getProfile(): Promise<{ success: boolean; data?: Driver; error?: string }> {
    try {
      const response = await httpClient.get<ProfileApiResponse>(
        API_ENDPOINTS.AUTH.PROFILE
      );

      if (response.success && response.data) {
        const driver: Driver = {
          id: response.data.id,
          email: response.data.email,
          firstName: response.data.firstName,
          lastName: response.data.lastName,
          phoneNumber: response.data.phoneNumber || '',
          licenseNumber: response.data.licenseNumber || '',
          employeeId: response.data.employeeId || '',
          profileImage: response.data.profileImage,
          isActive: response.data.isActive,
          createdAt: response.data.createdAt,
          updatedAt: response.data.updatedAt,
        };

        return {
          success: true,
          data: driver,
        };
      }

      return {
        success: false,
        error: response.message || 'Failed to get profile',
      };
    } catch (error) {
      console.error('Get profile error:', error);
      return {
        success: false,
        error: error instanceof ApiError ? error.message : 'Failed to get profile',
      };
    }
  }

  /**
   * Request password reset
   */
  async requestPasswordReset(email: string): Promise<{ success: boolean; message: string }> {
    try {
      const response = await httpClient.post(
        API_ENDPOINTS.AUTH.PASSWORD_RESET_REQUEST,
        { email: email.toLowerCase().trim() }
      );

      return {
        success: true,
        message: 'Password reset email sent',
      };
    } catch (error) {
      console.error('Password reset request error:', error);
      return {
        success: false,
        message: error instanceof ApiError ? error.message : 'Failed to request password reset',
      };
    }
  }
}

// Export singleton instance
export const authService = new AuthService();
