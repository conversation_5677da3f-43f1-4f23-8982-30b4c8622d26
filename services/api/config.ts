/**
 * API Configuration for Guzo Sync Backend
 * Based on the backend documentation endpoints
 */

// Get environment variables with fallbacks
const getApiBaseUrl = (): string => {
  const envUrl = process.env.EXPO_PUBLIC_API_BASE_URL;
  if (envUrl) {
    return envUrl;
  }

  // Fallback based on environment
  return __DEV__
    ? 'http://localhost:3000' // Development server
    : 'https://api.guzosync.com'; // Production server
};

const getApiTimeout = (): number => {
  const envTimeout = process.env.EXPO_PUBLIC_API_TIMEOUT;
  return envTimeout ? parseInt(envTimeout, 10) : 30000;
};

// API Base Configuration
export const API_CONFIG = {
  BASE_URL: getApiBaseUrl(),
  TIMEOUT: getApiTimeout(),

  // API Version
  VERSION: process.env.EXPO_PUBLIC_API_VERSION || 'v1',

  // Environment
  ENVIRONMENT: process.env.EXPO_PUBLIC_ENVIRONMENT || (__DEV__ ? 'development' : 'production'),

  // Debug flags
  DEBUG_API: process.env.EXPO_PUBLIC_DEBUG_API === 'true' || __DEV__,
  DEBUG_AUTH: process.env.EXPO_PUBLIC_DEBUG_AUTH === 'true' || __DEV__,

  // Request headers
  DEFAULT_HEADERS: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
} as const;

// API Endpoints based on backend documentation
export const API_ENDPOINTS = {
  // Authentication & Authorization
  AUTH: {
    LOGIN: '/api/accounts/login',
    LOGOUT: '/api/accounts/logout',
    REGISTER: '/api/accounts/register',
    PASSWORD_RESET_REQUEST: '/api/accounts/password/reset/request',
    PASSWORD_RESET_CONFIRM: '/api/accounts/password/reset/confirm',
    PROFILE: '/api/account/me',
    UPDATE_PROFILE: '/api/account/me',
  },
  
  // Driver specific endpoints
  DRIVER: {
    ATTENDANCE: '/api/drivers/attendance',
    ATTENDANCE_TODAY: '/api/drivers/attendance/today',
    INCIDENTS: '/api/drivers/incidents',
    ROUTE_CHANGE_REQUESTS: '/api/drivers/route-change-requests',
    SCHEDULES: '/api/drivers/schedules',
    ROUTE_SCHEDULE: '/api/drivers/routes/{routeId}/schedule',
    INSTRUCTIONS: '/api/drivers/instructions',
    ACKNOWLEDGE_INSTRUCTION: '/api/drivers/instructions/{instructionId}/acknowledge',
  },
  
  // Bus and Route information
  BUSES: {
    BUS_DETAILS: '/api/buses/{busId}',
    ROUTE_DETAILS: '/api/routes/{routeId}',
    BUS_STOPS: '/api/buses/stops',
    BUS_STOP_DETAILS: '/api/buses/stops/{busStopId}',
  },
  
  // Notifications
  NOTIFICATIONS: {
    LIST: '/api/notifications',
    MARK_READ: '/api/notifications/mark-read/{notificationId}',
    SETTINGS: '/api/account/notification-settings',
    BROADCAST: '/api/notifications/broadcast',
  },
  
  // Language Management
  LANGUAGE: {
    UPDATE_PREFERENCE: '/api/account/language',
    SUPPORTED_LANGUAGES: '/api/config/languages',
  },
} as const;

// WebSocket Events based on backend documentation
export const SOCKET_EVENTS = {
  // Bus tracking
  BUS_UPDATES: 'tracking.bus_updates',
  BUS_UPDATES_REQUEST: 'tracking.bus_updates.request',
  
  // Traffic information
  TRAFFIC_INFO: 'tracking.traffic_info',
  
  // Communication
  MESSAGE_SENT: 'conversation.message_sent',
  MESSAGE_RECEIVED: 'conversation.message_received',
} as const;

// HTTP Status Codes
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  INTERNAL_SERVER_ERROR: 500,
  SERVICE_UNAVAILABLE: 503,
} as const;

// Error Types
export const API_ERROR_TYPES = {
  NETWORK_ERROR: 'NETWORK_ERROR',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  AUTHENTICATION_ERROR: 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR: 'AUTHORIZATION_ERROR',
  SERVER_ERROR: 'SERVER_ERROR',
  UNKNOWN_ERROR: 'UNKNOWN_ERROR',
} as const;

// Request timeout configurations
export const TIMEOUT_CONFIG = {
  DEFAULT: 30000, // 30 seconds
  UPLOAD: 60000, // 1 minute for file uploads
  DOWNLOAD: 120000, // 2 minutes for downloads
  AUTHENTICATION: 15000, // 15 seconds for auth requests
} as const;
