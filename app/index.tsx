import { useEffect } from 'react';
import { useRouter } from 'expo-router';
import { useAuth } from '@/hooks/useAuth';
import { View, ActivityIndicator, StyleSheet } from 'react-native';
import { THEME_COLORS } from '@/constants/Auth';

export default function IndexPage() {
  const { state } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!state.isLoading) {
      if (state.isAuthenticated) {
        router.replace('/(driver)/dashboard' as any);
      } else {
        router.replace('/(auth)/login' as any);
      }
    }
  }, [state.isAuthenticated, state.isLoading, router]);

  return (
    <View style={styles.container}>
      <ActivityIndicator size="large" color={THEME_COLORS.primary} />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: THEME_COLORS.background,
  },
});
