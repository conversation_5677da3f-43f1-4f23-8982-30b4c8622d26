import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAuth } from '@/hooks/useAuth';
import { Button } from '@/components/ui/Button';
import { THEME_COLORS } from '@/constants/Auth';

export default function ProfileScreen() {
  const { state, logout } = useAuth();

  const handleLogout = async () => {
    await logout();
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>Driver Profile</Text>
        
        {state.driver && (
          <View style={styles.profileInfo}>
            <Text style={styles.label}>Name:</Text>
            <Text style={styles.value}>
              {state.driver.firstName} {state.driver.lastName}
            </Text>
            
            <Text style={styles.label}>Email:</Text>
            <Text style={styles.value}>{state.driver.email}</Text>
            
            <Text style={styles.label}>Phone:</Text>
            <Text style={styles.value}>{state.driver.phoneNumber}</Text>
            
            <Text style={styles.label}>License Number:</Text>
            <Text style={styles.value}>{state.driver.licenseNumber}</Text>
            
            <Text style={styles.label}>Employee ID:</Text>
            <Text style={styles.value}>{state.driver.employeeId}</Text>
          </View>
        )}
        
        <View style={styles.buttonContainer}>
          <Button
            title="Logout"
            onPress={handleLogout}
            variant="danger"
            fullWidth
          />
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: THEME_COLORS.background,
  },
  content: {
    flex: 1,
    padding: 24,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: THEME_COLORS.text,
    marginBottom: 32,
    textAlign: 'center',
  },
  profileInfo: {
    flex: 1,
    marginBottom: 32,
  },
  label: {
    fontSize: 14,
    fontWeight: '600',
    color: THEME_COLORS.textSecondary,
    marginTop: 16,
    marginBottom: 4,
  },
  value: {
    fontSize: 16,
    color: THEME_COLORS.text,
    paddingVertical: 8,
    paddingHorizontal: 12,
    backgroundColor: THEME_COLORS.surface,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: THEME_COLORS.border,
  },
  buttonContainer: {
    marginTop: 'auto',
  },
});
