import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAuth } from '@/hooks/useAuth';
import { Button } from '@/components/ui/Button';
import { THEME_COLORS } from '@/constants/Auth';

export default function DashboardScreen() {
  const { state, logout } = useAuth();

  const handleLogout = async () => {
    await logout();
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>Driver Dashboard</Text>
        <Text style={styles.welcome}>
          Welcome, {state.driver?.firstName} {state.driver?.lastName}!
        </Text>
        <Text style={styles.email}>
          {state.driver?.email}
        </Text>
        
        <View style={styles.buttonContainer}>
          <Button
            title="Logout"
            onPress={handleLogout}
            variant="outline"
          />
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: THEME_COLORS.background,
  },
  content: {
    flex: 1,
    padding: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: THEME_COLORS.text,
    marginBottom: 16,
  },
  welcome: {
    fontSize: 18,
    color: THEME_COLORS.text,
    marginBottom: 8,
    textAlign: 'center',
  },
  email: {
    fontSize: 14,
    color: THEME_COLORS.textSecondary,
    marginBottom: 32,
  },
  buttonContainer: {
    width: '100%',
    maxWidth: 200,
  },
});
