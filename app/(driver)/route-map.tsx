import * as Location from 'expo-location';
import { AppleMaps, GoogleMaps } from 'expo-maps';
import React, { useEffect, useState } from 'react';
import { Alert, Platform, StyleSheet, Text, View } from 'react-native';

interface LocationCoords {
  latitude: number;
  longitude: number;
}

export default function RouteMapScreen() {
  const [location, setLocation] = useState<LocationCoords | null>(null);

  useEffect(() => {
    (async () => {
      let { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(
          'Location Permission',
          'Permission to access location was denied. Please enable location services to use the map.',
          [{ text: 'OK' }]
        );
        return;
      }

      try {
        let currentLocation = await Location.getCurrentPositionAsync({});
        setLocation({
          latitude: currentLocation.coords.latitude,
          longitude: currentLocation.coords.longitude,
        });
      } catch (error) {
        Alert.alert('Error', 'Could not get your current location');
      }
    })();
  }, []);

  // Default location (Addis Ababa, Ethiopia) if location is not available
  const defaultLocation: LocationCoords = {
    latitude: 9.0320,
    longitude: 38.7469,
  };

  const currentLocation = location || defaultLocation;

  const renderMap = () => {
    if (Platform.OS === 'ios') {
      return (
        <AppleMaps.View
          style={styles.map}
          cameraPosition={{
            coordinates: currentLocation,
            zoom: 15,
          }}
          properties={{
            isTrafficEnabled: true,
          }}
          uiSettings={{
            myLocationButtonEnabled: true,
            compassEnabled: true,
            scaleBarEnabled: true,
          }}
          onCameraMove={(event) => {
            if (event.coordinates.latitude && event.coordinates.longitude) {
              setLocation({
                latitude: event.coordinates.latitude,
                longitude: event.coordinates.longitude,
              });
            }
          }}
        />
      );
    } else if (Platform.OS === 'android') {
      return (
        <GoogleMaps.View
          style={styles.map}
          cameraPosition={{
            coordinates: currentLocation,
            zoom: 15,
          }}
          properties={{
            isMyLocationEnabled: true,
            isTrafficEnabled: true,
            isBuildingEnabled: true,
          }}
          uiSettings={{
            myLocationButtonEnabled: true,
            compassEnabled: true,
            scaleBarEnabled: true,
            zoomControlsEnabled: true,
            rotationGesturesEnabled: true,
            scrollGesturesEnabled: true,
            tiltGesturesEnabled: true,
            zoomGesturesEnabled: true,
          }}
          onCameraMove={(event) => {
            if (event.coordinates.latitude && event.coordinates.longitude) {
              setLocation({
                latitude: event.coordinates.latitude,
                longitude: event.coordinates.longitude,
              });
            }
          }}
        />
      );
    } else {
      return (
        <View style={styles.fallbackContainer}>
          <Text style={styles.fallbackText}>
            Maps are only available on Android and iOS
          </Text>
        </View>
      );
    }
  };

  return <View style={styles.container}>{renderMap()}</View>;
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  map: {
    flex: 1,
  },
  fallbackContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  fallbackText: {
    fontSize: 16,
    textAlign: 'center',
    color: '#666',
  },
});
