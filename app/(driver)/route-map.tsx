import Constants from 'expo-constants';
import * as Location from 'expo-location';
import React, { useEffect, useState } from 'react';
import { Alert, Linking, Platform, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

interface LocationCoords {
  latitude: number;
  longitude: number;
  latitudeDelta?: number;
  longitudeDelta?: number;
}

// Check if we're running in Expo Go
const isExpoGo = Constants.executionEnvironment === 'storeClient';

export default function RouteMapScreen() {
  const [location, setLocation] = useState<LocationCoords | null>(null);

  useEffect(() => {
    (async () => {
      let { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(
          'Location Permission',
          'Permission to access location was denied. Please enable location services to use the map.',
          [{ text: 'OK' }]
        );
        return;
      }

      try {
        let currentLocation = await Location.getCurrentPositionAsync({});
        setLocation({
          latitude: currentLocation.coords.latitude,
          longitude: currentLocation.coords.longitude,
          latitudeDelta: 0.0922,
          longitudeDelta: 0.0421,
        });
      } catch (error) {
        Alert.alert('Error', 'Could not get your current location');
      }
    })();
  }, []);

  // Default location (Addis Ababa, Ethiopia) if location is not available
  const defaultLocation: LocationCoords = {
    latitude: 9.0320,
    longitude: 38.7469,
    latitudeDelta: 0.0922,
    longitudeDelta: 0.0421,
  };

  const currentLocation = location || defaultLocation;

  const openInMaps = () => {
    const { latitude, longitude } = currentLocation;
    const url = Platform.select({
      ios: `maps:0,0?q=${latitude},${longitude}`,
      android: `geo:0,0?q=${latitude},${longitude}`,
      default: `https://www.google.com/maps/search/?api=1&query=${latitude},${longitude}`,
    });

    if (url) {
      Linking.openURL(url);
    }
  };

  if (isExpoGo) {
    // Expo Go fallback - show a placeholder with option to open external maps
    return (
      <View style={styles.container}>
        <View style={styles.fallbackContainer}>
          <Text style={styles.title}>Map View</Text>
          <Text style={styles.subtitle}>
            Maps require a development build to work properly.
          </Text>
          <Text style={styles.locationText}>
            Current Location: {currentLocation.latitude.toFixed(4)}, {currentLocation.longitude.toFixed(4)}
          </Text>
          <TouchableOpacity style={styles.button} onPress={openInMaps}>
            <Text style={styles.buttonText}>Open in Maps App</Text>
          </TouchableOpacity>
          <Text style={styles.instructionText}>
            To see the full map functionality, create a development build with:
            {'\n'}npx expo run:android
          </Text>
        </View>
      </View>
    );
  }

  // This will only run in development builds where react-native-maps is available
  try {
    const MapView = require('react-native-maps').default;
    const { PROVIDER_GOOGLE } = require('react-native-maps');

    return (
      <View style={styles.container}>
        <MapView
          provider={PROVIDER_GOOGLE}
          style={styles.map}
          region={currentLocation}
          showsUserLocation={true}
          showsMyLocationButton={true}
          showsCompass={true}
          showsScale={true}
          showsBuildings={true}
          mapType="standard"
          onUserLocationChange={(event: any) => {
            if (event.nativeEvent.coordinate) {
              const { latitude, longitude } = event.nativeEvent.coordinate;
              setLocation({
                latitude,
                longitude,
                latitudeDelta: 0.0922,
                longitudeDelta: 0.0421,
              });
            }
          }}
        />
      </View>
    );
  } catch (error) {
    // Fallback if react-native-maps is not available
    return (
      <View style={styles.container}>
        <View style={styles.fallbackContainer}>
          <Text style={styles.title}>Map Not Available</Text>
          <Text style={styles.subtitle}>
            Please create a development build to use maps.
          </Text>
          <TouchableOpacity style={styles.button} onPress={openInMaps}>
            <Text style={styles.buttonText}>Open in Maps App</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  map: {
    flex: 1,
  },
  fallbackContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
    color: '#333',
  },
  subtitle: {
    fontSize: 16,
    marginBottom: 20,
    textAlign: 'center',
    color: '#666',
  },
  locationText: {
    fontSize: 14,
    marginBottom: 20,
    textAlign: 'center',
    color: '#444',
    fontFamily: 'monospace',
  },
  button: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    marginBottom: 20,
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  instructionText: {
    fontSize: 12,
    textAlign: 'center',
    color: '#888',
    fontFamily: 'monospace',
  },
});
