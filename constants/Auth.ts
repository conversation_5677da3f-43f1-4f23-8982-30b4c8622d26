export const AUTH_CONSTANTS = {
  // Storage keys
  STORAGE_KEYS: {
    TOKEN: 'auth_token',
    REFRESH_TOKEN: 'refresh_token',
    DRIVER_DATA: 'driver_data',
    REMEMBER_EMAIL: 'remember_email',
  },

  // Validation rules
  VALIDATION: {
    EMAIL: {
      MIN_LENGTH: 5,
      MAX_LENGTH: 100,
      PATTERN: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    },
    PASSWORD: {
      MIN_LENGTH: 6,
      MAX_LENGTH: 50,
    },
  },

  // Error messages
  ERRORS: {
    INVALID_EMAIL: 'Please enter a valid email address',
    EMAIL_REQUIRED: 'Email is required',
    PASSWORD_REQUIRED: 'Password is required',
    PASSWORD_TOO_SHORT: 'Password must be at least 6 characters',
    INVALID_CREDENTIALS: 'Invalid email or password',
    NETWORK_ERROR: 'Network error. Please check your connection',
    SERVER_ERROR: 'Server error. Please try again later',
    UNKNOWN_ERROR: 'An unexpected error occurred',
  },

  // API endpoints (will be configured later)
  API: {
    LOGIN: '/auth/driver/login',
    REFRESH: '/auth/refresh',
    LOGOUT: '/auth/logout',
    PROFILE: '/driver/profile',
  },

  // Token expiry buffer (5 minutes before actual expiry)
  TOKEN_REFRESH_BUFFER: 5 * 60 * 1000,
} as const;

export const THEME_COLORS = {
  primary: '#2563EB', // Blue
  primaryDark: '#1D4ED8',
  secondary: '#10B981', // Green
  danger: '#EF4444', // Red
  warning: '#F59E0B', // Amber
  background: '#FFFFFF',
  backgroundDark: '#1F2937',
  surface: '#F9FAFB',
  surfaceDark: '#374151',
  text: '#111827',
  textDark: '#F9FAFB',
  textSecondary: '#6B7280',
  border: '#E5E7EB',
  borderDark: '#4B5563',
  placeholder: '#9CA3AF',
} as const;
