export interface LoginCredentials {
  email: string;
  password: string;
}

export interface Driver {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  phoneNumber: string;
  licenseNumber: string;
  employeeId: string;
  profileImage?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface AuthResponse {
  success: boolean;
  message: string;
  data?: {
    driver: Driver;
    token: string;
    refreshToken: string;
    expiresIn: number;
  };
  error?: string;
}

export interface AuthState {
  isAuthenticated: boolean;
  driver: Driver | null;
  token: string | null;
  refreshToken: string | null;
  isLoading: boolean;
  error: string | null;
}

export interface LoginFormState {
  email: string;
  password: string;
  isLoading: boolean;
  errors: {
    email?: string;
    password?: string;
    general?: string;
  };
  showPassword: boolean;
}

export interface ValidationResult {
  isValid: boolean;
  errors: {
    email?: string;
    password?: string;
  };
}
