import React, { useState } from 'react';
import {
  View,
  TextInput as RNTextInput,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInputProps as RNTextInputProps,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { THEME_COLORS } from '@/constants/Auth';

interface TextInputProps extends Omit<RNTextInputProps, 'style'> {
  label?: string;
  error?: string;
  leftIcon?: keyof typeof Ionicons.glyphMap;
  rightIcon?: keyof typeof Ionicons.glyphMap;
  onRightIconPress?: () => void;
  containerStyle?: any;
  inputStyle?: any;
  showPasswordToggle?: boolean;
}

export const TextInput: React.FC<TextInputProps> = ({
  label,
  error,
  leftIcon,
  rightIcon,
  onRightIconPress,
  containerStyle,
  inputStyle,
  showPasswordToggle = false,
  secureTextEntry,
  ...props
}) => {
  const [isPasswordVisible, setIsPasswordVisible] = useState(false);
  const [isFocused, setIsFocused] = useState(false);

  const togglePasswordVisibility = () => {
    setIsPasswordVisible(!isPasswordVisible);
  };

  const handleFocus = () => {
    setIsFocused(true);
    props.onFocus?.({} as any);
  };

  const handleBlur = () => {
    setIsFocused(false);
    props.onBlur?.({} as any);
  };

  const getContainerStyle = () => {
    return [
      styles.container,
      isFocused && styles.containerFocused,
      error && styles.containerError,
      containerStyle,
    ];
  };

  const actualSecureTextEntry = showPasswordToggle 
    ? !isPasswordVisible 
    : secureTextEntry;

  const actualRightIcon = showPasswordToggle 
    ? (isPasswordVisible ? 'eye-off' : 'eye')
    : rightIcon;

  const actualOnRightIconPress = showPasswordToggle 
    ? togglePasswordVisibility 
    : onRightIconPress;

  return (
    <View style={styles.wrapper}>
      {label && <Text style={styles.label}>{label}</Text>}
      
      <View style={getContainerStyle()}>
        {leftIcon && (
          <Ionicons
            name={leftIcon}
            size={20}
            color={isFocused ? THEME_COLORS.primary : THEME_COLORS.textSecondary}
            style={styles.leftIcon}
          />
        )}
        
        <RNTextInput
          {...props}
          style={[styles.input, inputStyle]}
          onFocus={handleFocus}
          onBlur={handleBlur}
          secureTextEntry={actualSecureTextEntry}
          placeholderTextColor={THEME_COLORS.placeholder}
        />
        
        {(actualRightIcon || showPasswordToggle) && (
          <TouchableOpacity
            onPress={actualOnRightIconPress}
            style={styles.rightIconContainer}
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
          >
            <Ionicons
              name={actualRightIcon as keyof typeof Ionicons.glyphMap}
              size={20}
              color={isFocused ? THEME_COLORS.primary : THEME_COLORS.textSecondary}
            />
          </TouchableOpacity>
        )}
      </View>
      
      {error && <Text style={styles.errorText}>{error}</Text>}
    </View>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '600',
    color: THEME_COLORS.text,
    marginBottom: 8,
  },
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: THEME_COLORS.border,
    borderRadius: 8,
    backgroundColor: THEME_COLORS.background,
    minHeight: 48,
    paddingHorizontal: 12,
  },
  containerFocused: {
    borderColor: THEME_COLORS.primary,
    borderWidth: 2,
  },
  containerError: {
    borderColor: THEME_COLORS.danger,
  },
  leftIcon: {
    marginRight: 12,
  },
  input: {
    flex: 1,
    fontSize: 16,
    color: THEME_COLORS.text,
    paddingVertical: 12,
  },
  rightIconContainer: {
    padding: 4,
    marginLeft: 8,
  },
  errorText: {
    fontSize: 12,
    color: THEME_COLORS.danger,
    marginTop: 4,
    marginLeft: 4,
  },
});
