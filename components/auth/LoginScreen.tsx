import { Button } from '@/components/ui/Button';
import { TextInput } from '@/components/ui/TextInput';
import { THEME_COLORS } from '@/constants/Auth';
import { useAuth } from '@/hooks/useAuth';
import type { LoginFormState } from '@/types/auth';
import { sanitizeEmail, validateLoginForm } from '@/utils/validation';
import { StatusBar } from 'expo-status-bar';
import React, { useEffect, useState } from 'react';
import {
    Alert,
    KeyboardAvoidingView,
    Platform,
    ScrollView,
    StyleSheet,
    Text,
    View
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

export const LoginScreen: React.FC = () => {
  const { state: authState, login, clearError } = useAuth();
  
  const [formState, setFormState] = useState<LoginFormState>({
    email: '',
    password: '',
    isLoading: false,
    errors: {},
    showPassword: false,
  });

  // Clear auth errors when component mounts
  useEffect(() => {
    clearError();
  }, [clearError]);

  // Update form loading state based on auth state
  useEffect(() => {
    setFormState(prev => ({
      ...prev,
      isLoading: authState.isLoading,
    }));
  }, [authState.isLoading]);

  const handleEmailChange = (email: string) => {
    setFormState(prev => ({
      ...prev,
      email,
      errors: {
        ...prev.errors,
        email: undefined,
        general: undefined,
      },
    }));
  };

  const handlePasswordChange = (password: string) => {
    setFormState(prev => ({
      ...prev,
      password,
      errors: {
        ...prev.errors,
        password: undefined,
        general: undefined,
      },
    }));
  };

  const handleLogin = async () => {
    // Clear previous errors
    setFormState(prev => ({
      ...prev,
      errors: {},
    }));

    // Validate form
    const sanitizedEmail = sanitizeEmail(formState.email);
    const validation = validateLoginForm({
      email: sanitizedEmail,
      password: formState.password,
    });

    if (!validation.isValid) {
      setFormState(prev => ({
        ...prev,
        errors: validation.errors,
      }));
      return;
    }

    try {
      const response = await login({
        email: sanitizedEmail,
        password: formState.password,
      });

      if (!response.success) {
        setFormState(prev => ({
          ...prev,
          errors: {
            general: response.error || 'Login failed. Please try again.',
          },
        }));
      }
      // Success case is handled by the auth context and navigation
    } catch {
      setFormState(prev => ({
        ...prev,
        errors: {
          general: 'An unexpected error occurred. Please try again.',
        },
      }));
    }
  };

  const showForgotPasswordAlert = () => {
    Alert.alert(
      'Forgot Password',
      'Please contact your fleet manager to reset your password.',
      [{ text: 'OK' }]
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="dark" />
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidingView}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}
        >
          {/* Header Section */}
          <View style={styles.header}>
            <View style={styles.logoContainer}>
              <View style={styles.logoPlaceholder}>
                <Text style={styles.logoText}>GUZO</Text>
                <Text style={styles.logoSubtext}>SYNC</Text>
              </View>
            </View>
            <Text style={styles.title}>Driver Login</Text>
            <Text style={styles.subtitle}>
              Sign in to access your driver dashboard
            </Text>
          </View>

          {/* Form Section */}
          <View style={styles.form}>
            <TextInput
              label="Email Address"
              placeholder="Enter your email"
              value={formState.email}
              onChangeText={handleEmailChange}
              keyboardType="email-address"
              autoCapitalize="none"
              autoCorrect={false}
              leftIcon="mail"
              error={formState.errors.email}
              editable={!formState.isLoading}
            />

            <TextInput
              label="Password"
              placeholder="Enter your password"
              value={formState.password}
              onChangeText={handlePasswordChange}
              secureTextEntry
              showPasswordToggle
              leftIcon="lock-closed"
              error={formState.errors.password}
              editable={!formState.isLoading}
            />

            {/* General Error */}
            {formState.errors.general && (
              <View style={styles.errorContainer}>
                <Text style={styles.errorText}>
                  {formState.errors.general}
                </Text>
              </View>
            )}

            {/* Login Button */}
            <Button
              title="Sign In"
              onPress={handleLogin}
              loading={formState.isLoading}
              disabled={formState.isLoading}
              fullWidth
              size="large"
              style={styles.loginButton}
            />

            {/* Forgot Password */}
            <Button
              title="Forgot Password?"
              onPress={showForgotPasswordAlert}
              variant="outline"
              disabled={formState.isLoading}
              style={styles.forgotPasswordButton}
            />
          </View>

          {/* Footer */}
          <View style={styles.footer}>
            <Text style={styles.footerText}>
              Guzo Sync Driver App v1.0.0
            </Text>
            <Text style={styles.footerSubtext}>
              For support, contact your fleet manager
            </Text>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: THEME_COLORS.background,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: 24,
    paddingVertical: 20,
  },
  header: {
    alignItems: 'center',
    marginBottom: 40,
    paddingTop: 20,
  },
  logoContainer: {
    marginBottom: 24,
  },
  logoPlaceholder: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: THEME_COLORS.primary,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 8,
  },
  logoText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
    letterSpacing: 1,
  },
  logoSubtext: {
    fontSize: 10,
    fontWeight: '600',
    color: '#FFFFFF',
    letterSpacing: 2,
    marginTop: -2,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: THEME_COLORS.text,
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: THEME_COLORS.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
  },
  form: {
    flex: 1,
    marginBottom: 20,
  },
  errorContainer: {
    backgroundColor: '#FEF2F2',
    borderWidth: 1,
    borderColor: '#FECACA',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  errorText: {
    color: THEME_COLORS.danger,
    fontSize: 14,
    textAlign: 'center',
  },
  loginButton: {
    marginTop: 8,
    marginBottom: 16,
  },
  forgotPasswordButton: {
    marginTop: 8,
  },
  footer: {
    alignItems: 'center',
    paddingTop: 20,
    borderTopWidth: 1,
    borderTopColor: THEME_COLORS.border,
  },
  footerText: {
    fontSize: 12,
    color: THEME_COLORS.textSecondary,
    marginBottom: 4,
  },
  footerSubtext: {
    fontSize: 11,
    color: THEME_COLORS.placeholder,
    textAlign: 'center',
  },
});
