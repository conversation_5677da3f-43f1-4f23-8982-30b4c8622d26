import { THEME_COLORS } from '@/constants/Auth';
import { useAuth } from '@/hooks/useAuth';
import { useRouter, useSegments } from 'expo-router';
import React, { useEffect } from 'react';
import { ActivityIndicator, StyleSheet, View } from 'react-native';

export const AuthNavigator: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { state } = useAuth();
  const router = useRouter();
  const segments = useSegments();

  useEffect(() => {
    if (state.isLoading) {
      // Still loading auth state, don't navigate yet
      return;
    }

    const inAuthGroup = segments[0] === '(auth)';
    const inDriverGroup = segments[0] === '(driver)';

    if (state.isAuthenticated) {
      // User is authenticated
      if (inAuthGroup) {
        // Redirect from auth screens to driver dashboard
        router.replace('/(driver)/dashboard' as any);
      }
    } else {
      // User is not authenticated
      if (inDriverGroup || (!inAuthGroup && !inDriverGroup)) {
        // Redirect to login if trying to access protected routes or on initial load
        router.replace('/(auth)/login' as any);
      }
    }
  }, [state.isAuthenticated, state.isLoading, segments, router]);

  // Show loading screen while determining auth state
  if (state.isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={THEME_COLORS.primary} />
      </View>
    );
  }

  return <>{children}</>;
};

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: THEME_COLORS.background,
  },
});
