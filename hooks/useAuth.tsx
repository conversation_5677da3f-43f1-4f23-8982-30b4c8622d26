import { AUTH_CONSTANTS } from '@/constants/Auth';
import { authService } from '@/services/api/authService';
import type { AuthResponse, AuthState, Driver, LoginCredentials } from '@/types/auth';
import AsyncStorage from '@react-native-async-storage/async-storage';
import React, { createContext, ReactNode, useContext, useEffect, useReducer } from 'react';
// Additional React imports for optimization
const { useMemo, useCallback } = React;

// Auth Actions
type AuthAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'LOGIN_SUCCESS'; payload: { driver: Driver; token: string; refreshToken: string } }
  | { type: 'LOGOUT' }
  | { type: 'RESTORE_AUTH'; payload: { driver: Driver; token: string; refreshToken: string } };

// Initial state
const initialState: AuthState = {
  isAuthenticated: false,
  driver: null,
  token: null,
  refreshToken: null,
  isLoading: true, // Start with loading true for auth restoration
  error: null,
};

// Auth reducer
const authReducer = (state: AuthState, action: AuthAction): AuthState => {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };

    case 'SET_ERROR':
      return { ...state, error: action.payload, isLoading: false };

    case 'LOGIN_SUCCESS':
      return {
        ...state,
        isAuthenticated: true,
        driver: action.payload.driver,
        token: action.payload.token,
        refreshToken: action.payload.refreshToken,
        isLoading: false,
        error: null,
      };

    case 'LOGOUT':
      return {
        ...initialState,
        isLoading: false,
      };

    case 'RESTORE_AUTH':
      return {
        ...state,
        isAuthenticated: true,
        driver: action.payload.driver,
        token: action.payload.token,
        refreshToken: action.payload.refreshToken,
        isLoading: false,
        error: null,
      };

    default:
      return state;
  }
};

// Auth context
interface AuthContextType {
  state: AuthState;
  login: (credentials: LoginCredentials) => Promise<AuthResponse>;
  logout: () => Promise<void>;
  clearError: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Auth provider component
interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  const restoreAuth = useCallback(async () => {
    try {
      const [token, refreshToken, driverData] = await Promise.all([
        AsyncStorage.getItem(AUTH_CONSTANTS.STORAGE_KEYS.TOKEN),
        AsyncStorage.getItem(AUTH_CONSTANTS.STORAGE_KEYS.REFRESH_TOKEN),
        AsyncStorage.getItem(AUTH_CONSTANTS.STORAGE_KEYS.DRIVER_DATA),
      ]);

      if (token && refreshToken && driverData) {
        const driver = JSON.parse(driverData);
        dispatch({
          type: 'RESTORE_AUTH',
          payload: { driver, token, refreshToken },
        });
      } else {
        dispatch({ type: 'SET_LOADING', payload: false });
      }
    } catch (error) {
      console.error('Error restoring auth:', error);
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  }, []);

  // Restore authentication on app start
  useEffect(() => {
    restoreAuth();
  }, [restoreAuth]);

  const login = useCallback(async (credentials: LoginCredentials): Promise<AuthResponse> => {
    dispatch({ type: 'SET_LOADING', payload: true });
    dispatch({ type: 'SET_ERROR', payload: null });

    try {
      // Call the actual API service
      const response = await authService.login(credentials);

      if (response.success && response.data) {
        // Store auth data
        await Promise.all([
          AsyncStorage.setItem(AUTH_CONSTANTS.STORAGE_KEYS.TOKEN, response.data.token),
          AsyncStorage.setItem(AUTH_CONSTANTS.STORAGE_KEYS.REFRESH_TOKEN, response.data.refreshToken),
          AsyncStorage.setItem(AUTH_CONSTANTS.STORAGE_KEYS.DRIVER_DATA, JSON.stringify(response.data.driver)),
        ]);

        dispatch({
          type: 'LOGIN_SUCCESS',
          payload: {
            driver: response.data.driver,
            token: response.data.token,
            refreshToken: response.data.refreshToken,
          },
        });
      } else {
        // Handle login failure
        const errorMessage = response.error || response.message || AUTH_CONSTANTS.ERRORS.INVALID_CREDENTIALS;
        dispatch({ type: 'SET_ERROR', payload: errorMessage });
      }

      return response;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : AUTH_CONSTANTS.ERRORS.UNKNOWN_ERROR;
      dispatch({ type: 'SET_ERROR', payload: errorMessage });

      return {
        success: false,
        message: errorMessage,
        error: errorMessage,
      };
    }
  }, []);

  const logout = useCallback(async () => {
    try {
      // Call logout API (this will handle server-side session cleanup)
      await authService.logout();

      // Clear stored auth data
      await Promise.all([
        AsyncStorage.removeItem(AUTH_CONSTANTS.STORAGE_KEYS.TOKEN),
        AsyncStorage.removeItem(AUTH_CONSTANTS.STORAGE_KEYS.REFRESH_TOKEN),
        AsyncStorage.removeItem(AUTH_CONSTANTS.STORAGE_KEYS.DRIVER_DATA),
      ]);

      dispatch({ type: 'LOGOUT' });
    } catch (error) {
      console.error('Error during logout:', error);
      // Still dispatch logout even if API call or storage clear fails
      // Clear local storage anyway
      try {
        await Promise.all([
          AsyncStorage.removeItem(AUTH_CONSTANTS.STORAGE_KEYS.TOKEN),
          AsyncStorage.removeItem(AUTH_CONSTANTS.STORAGE_KEYS.REFRESH_TOKEN),
          AsyncStorage.removeItem(AUTH_CONSTANTS.STORAGE_KEYS.DRIVER_DATA),
        ]);
      } catch (storageError) {
        console.error('Error clearing storage during logout:', storageError);
      }
      dispatch({ type: 'LOGOUT' });
    }
  }, []);

  const clearError = useCallback(() => {
    dispatch({ type: 'SET_ERROR', payload: null });
  }, []);

  const value: AuthContextType = useMemo(() => ({
    state,
    login,
    logout,
    clearError,
  }), [state, login, logout, clearError]);

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

// Custom hook to use auth context
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
