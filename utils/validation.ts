import { AUTH_CONSTANTS } from '@/constants/Auth';
import type { ValidationResult, LoginCredentials } from '@/types/auth';

/**
 * Validates email format and requirements
 */
export const validateEmail = (email: string): { isValid: boolean; error?: string } => {
  if (!email.trim()) {
    return { isValid: false, error: AUTH_CONSTANTS.ERRORS.EMAIL_REQUIRED };
  }

  if (email.length < AUTH_CONSTANTS.VALIDATION.EMAIL.MIN_LENGTH) {
    return { isValid: false, error: AUTH_CONSTANTS.ERRORS.INVALID_EMAIL };
  }

  if (email.length > AUTH_CONSTANTS.VALIDATION.EMAIL.MAX_LENGTH) {
    return { isValid: false, error: AUTH_CONSTANTS.ERRORS.INVALID_EMAIL };
  }

  if (!AUTH_CONSTANTS.VALIDATION.EMAIL.PATTERN.test(email)) {
    return { isValid: false, error: AUTH_CONSTANTS.ERRORS.INVALID_EMAIL };
  }

  return { isValid: true };
};

/**
 * Validates password requirements
 */
export const validatePassword = (password: string): { isValid: boolean; error?: string } => {
  if (!password) {
    return { isValid: false, error: AUTH_CONSTANTS.ERRORS.PASSWORD_REQUIRED };
  }

  if (password.length < AUTH_CONSTANTS.VALIDATION.PASSWORD.MIN_LENGTH) {
    return { isValid: false, error: AUTH_CONSTANTS.ERRORS.PASSWORD_TOO_SHORT };
  }

  if (password.length > AUTH_CONSTANTS.VALIDATION.PASSWORD.MAX_LENGTH) {
    return { isValid: false, error: 'Password is too long' };
  }

  return { isValid: true };
};

/**
 * Validates login form data
 */
export const validateLoginForm = (credentials: LoginCredentials): ValidationResult => {
  const emailValidation = validateEmail(credentials.email);
  const passwordValidation = validatePassword(credentials.password);

  const errors: ValidationResult['errors'] = {};

  if (!emailValidation.isValid) {
    errors.email = emailValidation.error;
  }

  if (!passwordValidation.isValid) {
    errors.password = passwordValidation.error;
  }

  return {
    isValid: emailValidation.isValid && passwordValidation.isValid,
    errors,
  };
};

/**
 * Sanitizes email input
 */
export const sanitizeEmail = (email: string): string => {
  return email.trim().toLowerCase();
};

/**
 * Checks if email format is valid (for real-time validation)
 */
export const isValidEmailFormat = (email: string): boolean => {
  return AUTH_CONSTANTS.VALIDATION.EMAIL.PATTERN.test(email);
};
