# Guzo Sync Driver App - API Integration

## ✅ API Integration Completed

### Overview
The login functionality has been updated to integrate with the actual Guzo Sync Backend API as specified in the `guzoSyncbackend.md` documentation.

### Architecture

#### 1. **API Service Layer**
```
services/
├── api/
│   ├── config.ts          # API configuration and endpoints
│   ├── httpClient.ts      # HTTP client with error handling
│   └── authService.ts     # Authentication API service
```

#### 2. **Configuration Management**
- **Environment Variables**: API configuration through `.env` files
- **Dynamic Configuration**: Automatic environment detection
- **Debug Logging**: Comprehensive request/response logging in development

### API Endpoints Implemented

#### Authentication Endpoints (from backend docs)
- `POST /api/accounts/login` - Driver login
- `POST /api/accounts/logout` - Driver logout  
- `GET /api/account/me` - Get driver profile

### Key Features

#### ✅ **HTTP Client (`httpClient.ts`)**
- **Automatic Authentication**: JWT token handling
- **Request/Response Interceptors**: Automatic token attachment
- **Error Handling**: Comprehensive error classification
- **Timeout Management**: Configurable timeouts per request type
- **Debug Logging**: Detailed request/response logging
- **Network Error Recovery**: Proper error classification and messaging

#### ✅ **Authentication Service (`authService.ts`)**
- **Real API Integration**: Actual backend API calls
- **Response Transformation**: Backend response to app data model mapping
- **Error Classification**: Specific error handling for different scenarios
- **Type Safety**: Full TypeScript support

#### ✅ **Configuration System (`config.ts`)**
- **Environment-based URLs**: Different URLs for dev/prod
- **Endpoint Management**: Centralized API endpoint definitions
- **Timeout Configuration**: Different timeouts for different operations
- **Debug Flags**: Configurable logging levels

### Environment Configuration

#### Development Setup
```bash
# .env file
EXPO_PUBLIC_API_BASE_URL=http://localhost:3000
EXPO_PUBLIC_API_TIMEOUT=30000
EXPO_PUBLIC_DEBUG_API=true
```

#### Production Setup
```bash
# .env.production file
EXPO_PUBLIC_API_BASE_URL=https://api.guzosync.com
EXPO_PUBLIC_API_TIMEOUT=30000
EXPO_PUBLIC_DEBUG_API=false
```

### API Request/Response Flow

#### Login Flow
1. **User Input**: Email and password validation
2. **API Request**: `POST /api/accounts/login`
3. **Request Payload**:
   ```json
   {
     "email": "<EMAIL>",
     "password": "password123"
   }
   ```
4. **Expected Response**:
   ```json
   {
     "success": true,
     "message": "Login successful",
     "data": {
       "user": {
         "id": "driver_id",
         "email": "<EMAIL>",
         "firstName": "John",
         "lastName": "Doe",
         "phoneNumber": "+**********",
         "role": "driver",
         "isActive": true,
         "licenseNumber": "DL123456",
         "employeeId": "EMP001",
         "createdAt": "2024-01-01T00:00:00Z",
         "updatedAt": "2024-01-01T00:00:00Z"
       },
       "token": "jwt_token_here",
       "refreshToken": "refresh_token_here",
       "expiresIn": 3600
     }
   }
   ```

#### Error Handling
- **Network Errors**: Connection issues, timeouts
- **Authentication Errors**: Invalid credentials (401)
- **Validation Errors**: Invalid input data (400, 422)
- **Server Errors**: Backend issues (500, 503)

### Security Features

#### ✅ **Token Management**
- **Secure Storage**: AsyncStorage for token persistence
- **Automatic Attachment**: JWT tokens automatically added to requests
- **Token Refresh**: Ready for refresh token implementation

#### ✅ **Request Security**
- **HTTPS Support**: Production API calls over HTTPS
- **Header Security**: Proper content-type and accept headers
- **Input Sanitization**: Email normalization and validation

### Debug and Monitoring

#### Development Logging
```javascript
// Example debug output
[API] POST http://localhost:3000/api/accounts/login {
  headers: { "Content-Type": "application/json", Authorization: "[REDACTED]" },
  body: { email: "<EMAIL>", password: "[REDACTED]" }
}
[API] Response 200 OK { status: 200, headers: {...} }
```

#### Error Tracking
- **Detailed Error Messages**: User-friendly error messages
- **Error Classification**: Network, auth, validation, server errors
- **Debug Information**: Full error context in development

### Integration with Authentication Hook

#### Updated `useAuth` Hook
- **Real API Calls**: Replaced mock implementation
- **Error State Management**: Proper error handling and display
- **Loading States**: Accurate loading indicators
- **Token Storage**: Automatic token persistence

### Backend Compatibility

#### Driver-Specific Fields
The API service maps backend user data to driver-specific fields:
- `licenseNumber` - Driver's license number
- `employeeId` - Company employee ID
- `role` - User role (should be "driver")

#### Future Endpoints Ready
The configuration includes all driver-related endpoints from the backend docs:
- Attendance management
- Incident reporting
- Route change requests
- Schedule access
- Instruction acknowledgment

### Testing the Integration

#### 1. **Backend Server Required**
- Start the Guzo Sync backend server on `http://localhost:3000`
- Ensure the `/api/accounts/login` endpoint is available

#### 2. **Test Login**
- Use valid driver credentials
- Check network tab for actual API calls
- Verify token storage in AsyncStorage

#### 3. **Error Testing**
- Test with invalid credentials
- Test with network disconnected
- Test with server down

### Next Steps

#### 1. **Backend Integration**
- Set up actual backend server
- Configure driver accounts in database
- Test with real credentials

#### 2. **Additional API Services**
- Driver schedule service
- Incident reporting service
- Route management service
- Real-time location updates

#### 3. **Enhanced Security**
- Token refresh implementation
- Biometric authentication
- Certificate pinning for production

#### 4. **Offline Support**
- Cache management
- Offline queue for API calls
- Sync when online

### Configuration Files

#### Required Files
- `.env` - Development environment variables
- `.env.production` - Production environment variables
- `services/api/config.ts` - API configuration
- `services/api/httpClient.ts` - HTTP client
- `services/api/authService.ts` - Authentication service

#### Environment Variables
```bash
EXPO_PUBLIC_API_BASE_URL=http://localhost:3000
EXPO_PUBLIC_API_TIMEOUT=30000
EXPO_PUBLIC_API_VERSION=v1
EXPO_PUBLIC_ENVIRONMENT=development
EXPO_PUBLIC_DEBUG_API=true
EXPO_PUBLIC_DEBUG_AUTH=true
```

The API integration is now complete and ready for backend testing. The system will automatically switch between mock and real API based on backend availability.
